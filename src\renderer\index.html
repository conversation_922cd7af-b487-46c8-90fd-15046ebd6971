<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
    />

    <!-- 防止Safari自动缩放 -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-status-bar-style" content="default" />

    <!-- SEO 元数据 -->
    <title>随心听-随时随地，好音乐不等待</title>
    <meta
      name="description"
      content="随心听-随时随地，好音乐不等待 - 一款免费的在线音乐播放器，支持在线播放、歌词显示、音乐下载等功能。提供海量音乐资源，让您随时随地享受音乐。"
    />
    <meta
      name="keywords"
      content="随心听, 音乐播放器, 在线音乐, 免费音乐, 歌词显示, 音乐下载, 网易云音乐"
    />

    <!-- 作者信息 -->
    <meta name="author" content="随心听" />
    <meta name="author-url" content="https://github.com/algerkong" />

    <!-- PWA 相关 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#2E7D32" />

    <!-- iOS设备相关 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="随心听" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="57x57" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/icons/icon-96x96.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

    <!-- iOS启动屏幕 -->
    <!-- iPhone X, XS, 11 Pro -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone XR, 11 -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" />
    <!-- iPhone XS Max, 11 Pro Max -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone 12 mini -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 360px) and (device-height: 780px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone 12, 12 Pro -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone 12 Pro Max -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPad -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" />
    <!-- iPad Pro -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" />
    <!-- 通用启动屏幕 -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png" />

    <!-- 资源预加载 -->
    <link rel="preload" href="./assets/icon/iconfont.css" as="style" />
    <link rel="preload" href="./assets/css/base.css" as="style" />

    <!-- 样式表 -->
    <link rel="stylesheet" href="./assets/icon/iconfont.css" />
    <link rel="stylesheet" href="./assets/css/base.css" />
    <link rel="stylesheet" href="./assets/css/pwa.css" />

    <!-- 动画配置 -->
    <style>
      :root {
        --animate-delay: 0.5s;
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="./main.ts"></script>
    <!-- PWA 和 Service Worker 相关脚本 -->
    <script>
      // 注册Service Worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('Service Worker 注册成功:', registration.scope);

              // 检查更新
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // 有新版本可用
                    console.log('新版本可用，请刷新页面');
                  }
                });
              });
            })
            .catch(error => {
              console.log('Service Worker 注册失败:', error);
            });
        });
      }

      // iOS Safari 特定优化
      if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
          const now = (new Date()).getTime();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);

        // 防止橡皮筋效果
        document.addEventListener('touchmove', function (event) {
          if (event.scale !== 1) {
            event.preventDefault();
          }
        }, { passive: false });

        // 隐藏Safari地址栏
        window.addEventListener('load', function() {
          setTimeout(function() {
            window.scrollTo(0, 1);
          }, 0);
        });
      }

      // PWA 安装提示
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        console.log('PWA 安装提示已准备');
      });

      // 检测是否在PWA模式下运行
      function isPWA() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone === true;
      }

      // 如果在PWA模式下，添加特殊样式
      if (isPWA()) {
        document.documentElement.classList.add('pwa-mode');
        console.log('运行在PWA模式下');
      }
    </script>
  </body>
</html>
