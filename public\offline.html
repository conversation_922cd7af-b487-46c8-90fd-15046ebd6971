<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>随心听 - 离线</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
      color: #333;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 500px;
      padding: 30px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #4CAF50;
      margin-bottom: 20px;
    }
    p {
      line-height: 1.6;
      margin-bottom: 20px;
    }
    .icon {
      font-size: 64px;
      margin-bottom: 20px;
      color: #4CAF50;
    }
    .btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s;
    }
    .btn:hover {
      background-color: #45a049;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>您当前处于离线状态</h1>
    <p>无法连接到随心听服务。请检查您的网络连接，然后重试。</p>
    <p>连接恢复后，您可以继续享受随心听带来的音乐体验。</p>
    <button class="btn" onclick="window.location.reload()">重新连接</button>
  </div>
</body>
</html> 